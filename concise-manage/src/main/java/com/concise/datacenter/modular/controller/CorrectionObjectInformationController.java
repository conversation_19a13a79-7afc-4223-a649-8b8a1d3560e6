package com.concise.datacenter.modular.controller;


import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.base.param.BaseParam;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.datacenter.external.DateUtils;
import com.concise.datacenter.modular.service.DataSynchronizationService;
import com.concise.external.correctionobjectinformation.param.CorrectionObjectInformationParam;
import com.concise.external.correctionobjectinformation.service.CorrectionObjectInformationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 矫正对象信息表控制器
 *
 * <AUTHOR>
 * @date 2021-09-10 17:17:06
 */
@Api(tags = "矫正对象信息表")
@RestController
public class CorrectionObjectInformationController {

    @Resource
    private CorrectionObjectInformationService correctionObjectInformationService;
    @Resource
    private DataSynchronizationService dataSynchronizationService;

    /**
     * 查询矫正对象信息表
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    @Permission
    @ApiOperation(value = "矫正对象信息表_查询")
    @GetMapping("/correctionObjectInformation/page")
    @BusinessLog(title = "矫正对象信息表_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(CorrectionObjectInformationParam correctionObjectInformationParam) {
        return new SuccessResponseData(correctionObjectInformationService.page(correctionObjectInformationParam));
    }

    /**
     * 部分查询矫正对象信息表
     *
     * <AUTHOR>
     * @date 2025-08-03 17:17:06
     */
    @Permission
    @ApiOperation(value = "矫正对象信息表_部分查询")
    @GetMapping("/correctionObjectInformation/dataCenterPage")
    @BusinessLog(title = "矫正对象信息表_部分查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData dataCenterPage(CorrectionObjectInformationParam correctionObjectInformationParam) {
        return new SuccessResponseData(correctionObjectInformationService.dataCenterPage(correctionObjectInformationParam));
    }

    /**
     * 矫正对象数据同步
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    @ApiOperation(value = "矫正对象数据同步")
    @GetMapping("/correctionObjectInformation/getCorrectionObjectInformationByDate")
    public ResponseData getCorrectionObjectInformationByDate(@RequestParam(name = "yyyy", defaultValue = "2020") Integer yyyy,
                                                             @RequestParam(name = "MM", defaultValue = "1") Integer MM,
                                                             @RequestParam(name = "dd", defaultValue = "1") Integer dd) {

        dataSynchronizationService.allCorrectionObjectInformationByDate(DateUtils.date2Str(new Date(),
                new SimpleDateFormat(yyyy + "-" + MM + "-" + dd + "'T'HH:mm:ss.SSS")));
        return new SuccessResponseData();
    }


    /**
     * 添加矫正对象信息表
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    @Permission
    @PostMapping("/correctionObjectInformation/add")
    @BusinessLog(title = "矫正对象信息表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(BaseParam.add.class) CorrectionObjectInformationParam correctionObjectInformationParam) {
        correctionObjectInformationService.add(correctionObjectInformationParam);
        return new SuccessResponseData();
    }

    /**
     * 删除矫正对象信息表，可批量删除
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    @Permission
    @PostMapping("/correctionObjectInformation/delete")
    @BusinessLog(title = "矫正对象信息表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(BaseParam.delete.class) List<CorrectionObjectInformationParam> correctionObjectInformationParamList) {
        correctionObjectInformationService.delete(correctionObjectInformationParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑矫正对象信息表
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    @Permission
    @PostMapping("/correctionObjectInformation/edit")
    @BusinessLog(title = "矫正对象信息表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(BaseParam.edit.class) CorrectionObjectInformationParam correctionObjectInformationParam) {
        correctionObjectInformationService.edit(correctionObjectInformationParam);
        return new SuccessResponseData();
    }

    /**
     * 查看矫正对象信息表
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    @Permission
    @GetMapping("/correctionObjectInformation/detail")
    @BusinessLog(title = "矫正对象信息表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(BaseParam.detail.class) CorrectionObjectInformationParam correctionObjectInformationParam) {
        return new SuccessResponseData(correctionObjectInformationService.detail(correctionObjectInformationParam));
    }

    /**
     * @return
     */
    @ApiOperation(value = "获取token")
    @GetMapping("/getToken")
    public ResponseData getToken() {
        return new SuccessResponseData(dataSynchronizationService.getToken());
    }

    /**
     * 矫正对象信息表列表
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    @Permission
    @GetMapping("/correctionObjectInformation/list")
    @BusinessLog(title = "矫正对象信息表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(CorrectionObjectInformationParam correctionObjectInformationParam) {
        return new SuccessResponseData(correctionObjectInformationService.list(correctionObjectInformationParam));
    }

    /**
     * 导出系统用户
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    @Permission
    @GetMapping("/correctionObjectInformation/export")
    @BusinessLog(title = "矫正对象信息表_导出", opType = LogAnnotionOpTypeEnum.EXPORT)
    public void export(CorrectionObjectInformationParam correctionObjectInformationParam) {
        correctionObjectInformationService.export(correctionObjectInformationParam);
    }


    public static void main(String[] args) throws InterruptedException {
        String code = "400";
        while (code.equals("400")) {
            Thread.sleep(5000);
            System.out.println("暂停结束");
            try {
                System.out.println("尝试一下");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

}
