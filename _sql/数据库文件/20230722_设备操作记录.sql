
CREATE TABLE correction_device_operator (
      id varchar(32) NOT NULL COMMENT 'ID',
      pid varchar(32) NOT NULL COMMENT '万达矫正对象id',
      sqjzry_id VARCHAR(32)  DEFAULT NULL COMMENT '云雀矫正人员标识',
      op_day INT COMMENT '操作日期|格式：YYYYMMDD',
      op_time varchar(14) DEFAULT NULL COMMENT '操作时间 yyyyMMddHHmmss',
      device_code varchar(20) DEFAULT NULL COMMENT '设备编码',
      jzjg varchar(32) DEFAULT NULL COMMENT '机构id',
      type tinyint(1) DEFAULT 0 COMMENT '业务类型（0：日常报告）',
      PRIMARY KEY (id)
) COMMENT='设备操作记录';


